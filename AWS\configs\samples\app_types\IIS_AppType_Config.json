{"APP_TYPE": "iis", "description": "Internet Information Services web server configuration", "ou_suffix": "IIS Server", "disk_configuration": {"allocation_unit_size": "4096", "file_system": "NTFS", "drives": [{"drive_letter": "C", "allocation_unit_size": "4096", "purpose": "System"}, {"drive_letter": "D", "allocation_unit_size": "4096", "purpose": "Web Content"}, {"drive_letter": "E", "allocation_unit_size": "4096", "purpose": "Logs"}]}, "windows_features": ["IIS-WebServerRole", "IIS-WebServer", "IIS-CommonHttpFeatures", "IIS-HttpErrors", "IIS-HttpRedirect", "IIS-ApplicationDevelopment", "IIS-NetFxExtensibility45", "IIS-HealthAndDiagnostics", "IIS-HttpLogging", "IIS-LoggingLibraries", "IIS-RequestMonitor", "IIS-HttpTracing", "IIS-Security", "IIS-RequestFiltering", "IIS-CertProvider", "IIS-IISCertificateMappingAuthentication", "IIS-Performance", "IIS-HttpCompressionStatic", "IIS-HttpCompressionDynamic", "IIS-WebServerManagementTools", "IIS-ManagementConsole", "IIS-IIS6ManagementCompatibility", "IIS-Metabase", "IIS-ASPNET45"], "firewall_rules": [{"name": "HTTP-In", "port": "80", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "HTTPS-In", "port": "443", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "FTP-In", "port": "21", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "RDP-In", "port": "3389", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}], "registry_settings": [{"path": "HKLM\\SYSTEM\\CurrentControlSet\\Services\\HTTP\\Parameters", "name": "MaxConnections", "value": "65536", "type": "DWORD"}, {"path": "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server", "name": "fDenyTSConnections", "value": "0", "type": "DWORD"}], "services": [{"name": "W3SVC", "startup_type": "Automatic", "description": "World Wide Web Publishing Service"}, {"name": "WAS", "startup_type": "Automatic", "description": "Windows Process Activation Service"}, {"name": "IISADMIN", "startup_type": "Automatic", "description": "IIS Admin Service"}], "iis_settings": {"default_app_pool": {"name": "DefaultAppPool", "managed_runtime_version": "v4.0", "identity": "ApplicationPoolIdentity", "idle_timeout": "00:20:00"}, "compression": {"static": "true", "dynamic": "true"}, "logging": {"format": "W3C", "directory": "E:\\inetpub\\logs\\LogFiles"}}, "performance_settings": {"page_file": "auto", "virtual_memory": "auto"}}