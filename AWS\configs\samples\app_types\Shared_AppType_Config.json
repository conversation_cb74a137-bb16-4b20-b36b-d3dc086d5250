{"APP_TYPE": "shared", "description": "Standard shared server configuration for general workloads", "ou_suffix": "Shared", "disk_configuration": {"allocation_unit_size": "4096", "file_system": "NTFS", "drives": [{"drive_letter": "C", "allocation_unit_size": "4096", "purpose": "System"}, {"drive_letter": "D", "allocation_unit_size": "4096", "purpose": "Data"}]}, "windows_features": ["IIS-WebServerRole", "IIS-WebServer", "IIS-CommonHttpFeatures", "IIS-HttpErrors", "IIS-HttpRedirect", "IIS-ApplicationDevelopment", "IIS-NetFxExtensibility45", "IIS-HealthAndDiagnostics", "IIS-HttpLogging", "IIS-Security", "IIS-RequestFiltering", "IIS-Performance", "IIS-WebServerManagementTools", "IIS-ManagementConsole", "IIS-IIS6ManagementCompatibility", "IIS-Metabase"], "firewall_rules": [{"name": "HTTP-In", "port": "80", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "HTTPS-In", "port": "443", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "RDP-In", "port": "3389", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}], "registry_settings": [{"path": "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server", "name": "fDenyTSConnections", "value": "0", "type": "DWORD"}], "services": [{"name": "W3SVC", "startup_type": "Automatic", "description": "World Wide Web Publishing Service"}, {"name": "WAS", "startup_type": "Automatic", "description": "Windows Process Activation Service"}], "performance_settings": {"page_file": "auto", "virtual_memory": "auto"}}